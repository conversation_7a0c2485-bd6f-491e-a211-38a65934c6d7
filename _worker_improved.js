import { connect } from 'cloudflare:sockets';

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct', // Optional direct relayip relaysocks Default direct
        retryMode: 'relayip', // Optional relayip relaysocks Default relayip
    },

    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },

    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        // Example:  user:pass@host:port  or  host:port
        socks: 'web5.serv00.com:13668',
        // socks: 'user:<EMAIL>:13668',
    },

    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt', // https://raw.almain126.changeip.org/az/index.txt
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },

    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

export default {
    async fetch(request, env, ctx) {
        try {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses; // API_ADDRESSES_URL
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2; // API_ADDRESSES_URL_2
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        // return new Response(JSON.stringify(request.cf), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 2), { status: 200 });
                        // return new Response(JSON.stringify(request.cf, null, 4), { status: 200 });
                        return new Response(null, { status: 204 });
                    case `/a`: {
                        return new Response(null, { status: 204 });
                    }
                    case `/z`: {
                        const newResponse = new Response(null, { status: 204 });
                        console.log('Status:', newResponse.status);
                        return newResponse;
                    }
                    case `/aazz`: {
                        try {
                            const inputString = await fetchRemoteData(globalSessionConfig.api.addresses);
                            // const inputTemplate = await fetchRemoteData(globalSessionConfig.api.directTemplate);
                            const inputTemplate = await fetchRemoteData(globalSessionConfig.api.globalTemplate);

                            const getConfig = (type, tls) =>
                                url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64')
                                    ? (() => {
                                        const configs = generateConfigs(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'));
                                        if (type === 'both') return btoa(configs.protocolP0 + '\n' + configs.protocolP1);
                                        if (type === 'p0') return btoa(configs.protocolP0);
                                        if (type === 'p1') return btoa(configs.protocolP1);
                                    })()
                                    : getCustomConfig(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'), type, inputTemplate);

                            const configs = {
                                both: getConfig('both', true),
                                p0: getConfig('p0', true),
                                p1: getConfig('p1', true),
                                bothNotls: getConfig('both', false),
                                p0Notls: getConfig('p0', false),
                                p1Notls: getConfig('p1', false)
                            };

                            const configMappings = [
                                { param: 'both', config: configs.both },
                                { param: 'az', config: configs.p0 },
                                { param: 'za', config: configs.p1 },
                                { param: 'both-notls', config: configs.bothNotls },
                                { param: 'az-notls', config: configs.p0Notls },
                                { param: 'za-notls', config: configs.p1Notls }
                            ];

                            const getResponseConfig = (isMozilla) => ({
                                status: 200,
                                headers: {
                                    "Content-Type": "text/plain;charset=utf-8",
                                    ...(isMozilla ? {} : {
                                        "Content-Disposition": `attachment; filename=${globalSessionConfig.misc.subName}; filename*=utf-8''${encodeURIComponent(globalSessionConfig.misc.subName)}`,
                                        "Profile-Update-Interval": "6"
                                    })
                                }
                            });

                            const isMozilla = userAgent && userAgent.includes('mozilla');
                            const prefixes = ['b64', 'base64'];

                            for (const prefix of prefixes) {
                                for (const { param, config } of configMappings) {
                                    const fullParam = `${prefix}${param}`;
                                    if (url.searchParams.has(fullParam)) {
                                        return new Response(config, getResponseConfig(isMozilla));
                                    }
                                }
                            }

                            for (const { param, config } of configMappings) {
                                if (url.searchParams.has(param)) {
                                    return new Response(config, getResponseConfig(isMozilla));
                                }
                            }

                            // Default case
                            // return new Response(configMappings[0].config, getResponseConfig(isMozilla));
                            // Default case
                            const isB64Request = url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64');
                            const defaultConfig = isB64Request
                                ? getConfig('both', true)
                                : configMappings[0].config;

                            return new Response(defaultConfig, getResponseConfig(isMozilla));

                        } catch (error) {
                            return new Response(error.message, { status: 400 });
                        }
                    }

                    default:
                        return new Response('Not found', { status: 404 });
                    // return new Response('Expected Upgrade: websocket', { status: 426 });
                }
                // } else {
            } else if (upgradeHeader === 'websocket') {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    // globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    // globalSessionConfig.relay.socks = url.pathname.split('/socks=/i')[1].split('/')[0]?.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const handleSessionA = (request, env, ctx) => handleSession(request, env, ctx, globalControllerConfig.targetProtocolType0);
                // const handleSessionZ = (request, env, ctx) => processSession(request, env, ctx, globalControllerConfig.targetProtocolType1);

                const HANDLER_CHOICE = 1;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                // const pathHandlers = {
                //     [globalControllerConfig.targetPathType1]: handleSessionZ,
                //     [globalControllerConfig.targetPathType0]: handleSessionA
                // }
                // const pathType = url.pathname.split('/')[1];
                // const handler = pathHandlers[pathType] || handleSessionA
                // // return handler(request, env, ctx)
                // return await handler(request, env, ctx)
                // // return (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)
                // // return await (pathHandlers[pathType] ? pathHandlers[pathType] : handleSessionA)(request, env, ctx)

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                // return handler(request, env, ctx)
                return await handler(request, env, ctx)
            }
        } catch (error) {
            return new Response(`fetch Error: ${error.message}`, { status: 500 });
            // return new Response(error.toString());
            // return new Response(error.toString(), { status: 400 });
            // return new Response(error.message, { status: 400 });
            // return new Response(error.stack || error.message, { status: 400 });
        }
    },
};

// 🔧 改进版本的 handleSession 函数 - 添加完善的错误处理
export async function handleSession(request, env, ctx, protocolMode) {
    let client = null;
    let server = null;
    let tcpInterface = null;
    let tcpReader = null;
    let tcpWriter = null;
    let upstreamReadable = null;
    let isSessionClosed = false;

    try {
        // 🛡️ 1. 安全创建 WebSocket 对
        const { 0: clientSocket, 1: serverSocket } = Object.values(new WebSocketPair());
        client = clientSocket;
        server = serverSocket;
        
        // 🛡️ 2. 安全接受连接
        try {
            server.accept();
        } catch (acceptError) {
            console.error('WebSocket accept failed:', acceptError);
            return new Response('WebSocket accept failed', { status: 500 });
        }

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";

        /* ingress modes: transform | readable */
        let ingressMode = "transform";   // ← Change here to switch modes

        // 🛡️ 3. 安全的清理函数
        const safeCleanup = (reason = 'Unknown error') => {
            if (isSessionClosed) return;
            isSessionClosed = true;

            console.log(`Session cleanup triggered: ${reason}`);

            // 清理 TCP 连接
            if (tcpReader) {
                try { tcpReader.releaseLock(); } catch (e) { console.warn('tcpReader releaseLock failed:', e); }
                tcpReader = null;
            }
            if (tcpWriter) {
                try { tcpWriter.releaseLock(); } catch (e) { console.warn('tcpWriter releaseLock failed:', e); }
                tcpWriter = null;
            }
            if (tcpInterface) {
                try { 
                    if (typeof tcpInterface.close === 'function') {
                        tcpInterface.close();
                    }
                } catch (e) { console.warn('tcpInterface close failed:', e); }
                tcpInterface = null;
            }

            // 清理 WebSocket 连接
            if (server) {
                try {
                    if (server.readyState === WS_STATES.OPEN || server.readyState === WS_STATES.CONNECTING) {
                        server.close(1013, reason);
                    }
                } catch (e) { console.warn('server close failed:', e); }
            }

            // 清理流
            if (upstreamReadable) {
                try {
                    upstreamReadable.cancel(reason).catch(() => {});
                } catch (e) { console.warn('upstreamReadable cancel failed:', e); }
                upstreamReadable = null;
            }
        };

        // 🛡️ 4. 安全的 WebSocket 发送函数
        const safeSend = (data) => {
            try {
                if (server && server.readyState === WS_STATES.OPEN && !isSessionClosed) {
                    server.send(data);
                    return true;
                }
                return false;
            } catch (error) {
                console.warn('WebSocket send failed:', error);
                safeCleanup('WebSocket send failed');
                return false;
            }
        };

        if (ingressMode === "transform") {
            /* ----- TransformStream with IIFE implementation ----- */
            upstreamReadable = ((transformStream) => {
                let holdWriter = null;
                
                try {
                    holdWriter = transformStream.writable.getWriter();
                } catch (writerError) {
                    console.error('Failed to get transform writer:', writerError);
                    safeCleanup('Transform writer creation failed');
                    return transformStream.readable;
                }

                // 🛡️ 5. 安全处理 early header
                if (earlyHeader) {
                    try {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData && holdWriter) {
                            holdWriter.write(earlyData).catch((writeError) => {
                                console.warn('Early header write failed:', writeError);
                            });
                        }
                    } catch (decodeError) {
                        console.warn('Early header decode failed:', decodeError);
                    }
                }

                const handleMessage = (e) => {
                    try {
                        if (holdWriter && !isSessionClosed) {
                            holdWriter.write(e.data).catch((writeError) => {
                                console.warn('Message write failed:', writeError);
                                safeCleanup('Message write failed');
                            });
                        }
                    } catch (error) {
                        console.warn('Message handler error:', error);
                        safeCleanup('Message handler error');
                    }
                };

                const handleClose = (e) => {
                    try {
                        if (holdWriter && !isSessionClosed) {
                            holdWriter.close().catch((closeError) => {
                                console.warn('Writer close failed:', closeError);
                            });
                        }
                        safeCleanup('WebSocket closed');
                    } catch (error) {
                        console.warn('Close handler error:', error);
                    }
                };

                const handleError = (e) => {
                    console.error('WebSocket error:', e);
                    try {
                        if (holdWriter && !isSessionClosed) {
                            holdWriter.abort().catch((abortError) => {
                                console.warn('Writer abort failed:', abortError);
                            });
                        }
                        safeCleanup('WebSocket error');
                    } catch (error) {
                        console.warn('Error handler error:', error);
                    }
                };

                // 🛡️ 6. 安全绑定事件处理器
                try {
                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                } catch (bindError) {
                    console.error('Event binding failed:', bindError);
                    safeCleanup('Event binding failed');
                }

                return transformStream.readable;
            })(
                new TransformStream()
            )

        } else if (ingressMode === "readable") {
            /* ----- ReadableStream implementation ----- */
            upstreamReadable = new ReadableStream({
                start(controller) {
                    // 🛡️ 7. 安全处理 early header (ReadableStream 版本)
                    if (earlyHeader) {
                        try {
                            const earlyData = decodeBase64Url(earlyHeader);
                            if (earlyData) {
                                controller.enqueue(earlyData);
                            }
                        } catch (decodeError) {
                            console.warn('Early header decode failed (readable):', decodeError);
                        }
                    }

                    const handleMessage = (e) => {
                        try {
                            if (!isSessionClosed) {
                                controller.enqueue(e.data);
                            }
                        } catch (error) {
                            console.warn('Message enqueue failed:', error);
                            safeCleanup('Message enqueue failed');
                        }
                    };

                    const handleClose = (e) => {
                        try {
                            if (!isSessionClosed) {
                                controller.close();
                            }
                            safeCleanup('WebSocket closed (readable)');
                        } catch (error) {
                            console.warn('Controller close failed:', error);
                        }
                    };

                    const handleError = (e) => {
                        console.error('WebSocket error (readable):', e);
                        try {
                            if (!isSessionClosed) {
                                controller.error(e);
                            }
                            safeCleanup('WebSocket error (readable)');
                        } catch (error) {
                            console.warn('Controller error failed:', error);
                        }
                    };

                    // 🛡️ 8. 安全绑定事件处理器 (ReadableStream 版本)
                    try {
                        server['onmessage'] = handleMessage;
                        server['onclose'] = handleClose;
                        server['onerror'] = handleError;
                    } catch (bindError) {
                        console.error('Event binding failed (readable):', bindError);
                        safeCleanup('Event binding failed (readable)');
                    }
                },
            });
        }

        // 🛡️ 9. 安全的 pipeTo 操作 - 这是关键改进
        const pipePromise = upstreamReadable.pipeTo(
            new WritableStream({
                async write(chunk) {
                    try {
                        if (isSessionClosed) {
                            throw new Error('Session already closed');
                        }

                        if (tcpWriter) {
                            await tcpWriter.write(chunk);
                            return;
                        }

                        // 🛡️ 10. 安全解析协议头
                        let header;
                        try {
                            header = await parseProtocolHeader(chunk, server, protocolMode);
                        } catch (parseError) {
                            console.error('Protocol header parse failed:', parseError);
                            safeCleanup('Protocol header parse failed');
                            throw parseError;
                        }

                        // 🛡️ 11. 安全的连接建立 - 双重重试机制
                        try {
                            tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                            await tcpInterface.opened;
                        } catch (connectError) {
                            console.warn('Primary connection failed, trying retry mode:', connectError);
                            try {
                                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                            } catch (retryError) {
                                console.error('Both connection attempts failed:', retryError);
                                safeCleanup('Connection failed');
                                throw new Error(`Connection failed: ${retryError.message}`);
                            }
                        }

                        // 🛡️ 12. 安全获取 TCP writer
                        try {
                            tcpWriter = tcpInterface.writable.getWriter();
                        } catch (writerError) {
                            console.error('Failed to get TCP writer:', writerError);
                            safeCleanup('TCP writer creation failed');
                            throw writerError;
                        }

                        // 🛡️ 13. 安全发送协议响应
                        if (protocolMode === globalControllerConfig.targetProtocolType0) {
                            try {
                                const responseData = Uint8Array.of(header.version, 0);
                                if (!safeSend(responseData)) {
                                    throw new Error('Failed to send protocol response');
                                }
                            } catch (sendError) {
                                console.error('Protocol response send failed:', sendError);
                                safeCleanup('Protocol response send failed');
                                throw sendError;
                            }
                        }

                        // 🛡️ 14. 安全写入初始数据
                        if (header.rawClientData) {
                            try {
                                await tcpWriter.write(header.rawClientData);
                            } catch (writeError) {
                                console.error('Raw client data write failed:', writeError);
                                safeCleanup('Raw client data write failed');
                                throw writeError;
                            }
                        }

                        // 🛡️ 15. 安全的 TCP 到 WebSocket 数据转发
                        (async () => {
                            try {
                                tcpReader = tcpInterface.readable.getReader();
                                
                                while (!isSessionClosed) {
                                    let result;
                                    try {
                                        result = await tcpReader.read();
                                    } catch (readError) {
                                        console.warn('TCP read failed:', readError);
                                        break;
                                    }

                                    const { value, done } = result;
                                    if (done || !value) {
                                        console.log('TCP stream ended');
                                        break;
                                    }

                                    if (!safeSend(value)) {
                                        console.warn('WebSocket send failed, stopping TCP read loop');
                                        break;
                                    }
                                }
                            } catch (readerError) {
                                console.error('TCP reader error:', readerError);
                            } finally {
                                // 清理 TCP reader
                                if (tcpReader) {
                                    try { 
                                        tcpReader.releaseLock(); 
                                    } catch (releaseError) { 
                                        console.warn('TCP reader release failed:', releaseError);
                                    }
                                }
                                safeCleanup('TCP reader finished');
                            }
                        })().catch((asyncError) => {
                            console.error('Async TCP reader error:', asyncError);
                            safeCleanup('Async TCP reader error');
                        });

                    } catch (writeError) {
                        console.error('WritableStream write error:', writeError);
                        safeCleanup('WritableStream write error');
                        throw writeError;
                    }
                },
                close() {
                    console.log('WritableStream closed');
                    safeCleanup('WritableStream closed');
                },
                abort(reason) {
                    console.error('WritableStream aborted:', reason);
                    safeCleanup('WritableStream aborted');
                }
            })
        ).catch((pipeError) => {
            // 🛡️ 16. 关键改进：捕获 pipeTo 的异常
            console.error('PipeTo operation failed:', pipeError);
            safeCleanup('PipeTo operation failed');
            
            // 不要重新抛出异常，而是优雅处理
            return Promise.resolve();
        });

        // 🛡️ 17. 设置超时保护
        const timeoutId = setTimeout(() => {
            if (!isSessionClosed) {
                console.warn('Session timeout, cleaning up');
                safeCleanup('Session timeout');
            }
        }, 300000); // 5分钟超时

        // 清理超时定时器
        pipePromise.finally(() => {
            clearTimeout(timeoutId);
        });

        return new Response(null, { status: 101, webSocket: client });

    } catch (sessionError) {
        // 🛡️ 18. 顶层异常捕获 - 这是最重要的改进
        console.error('HandleSession top-level error:', sessionError);
        
        // 执行清理
        if (!isSessionClosed) {
            try {
                if (tcpReader) { tcpReader.releaseLock(); }
                if (tcpWriter) { tcpWriter.releaseLock(); }
                if (tcpInterface && typeof tcpInterface.close === 'function') { tcpInterface.close(); }
                if (server && server.readyState === WS_STATES.OPEN) { server.close(1013, 'Session error'); }
                if (upstreamReadable) { upstreamReadable.cancel('Session error').catch(() => {}); }
            } catch (cleanupError) {
                console.warn('Cleanup error:', cleanupError);
            }
        }

        // 返回错误响应而不是抛出异常
        return new Response(`Session Error: ${sessionError.message}`, { status: 500 });
    }
}

/* Single-dial wrapper */
async function dial(header, mode, protocolMode) {
    const iface = await createConnection(header, mode, protocolMode);
    await iface.opened; // Return after the connection is fully established
    return iface;
}

/**
 * @param {any} header
 * @param {'relayip' | 'relaysocks' | 'direct' | string} mode
 * @param {any} protocolMode
 * @returns {Promise<any>}
**/
async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    // const connectionHandlers = {
    //     'relayip': () => {
    //         const needDirect =
    //             [1].includes(addressType) ||
    //             (useTargetProtocol && [3].includes(addressType)) ||
    //             (!useTargetProtocol && [4].includes(addressType));
    //         return needDirect
    //             ? connect({ hostname: addressRemote, port: portRemote })
    //             : connect({
    //                 hostname: globalSessionConfig.relay.ip,
    //                 port: globalSessionConfig.relay.port || portRemote,
    //             });
    //     },
    //     'relaysocks': () => {
    //         return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
    //     },
    //     'direct': () => {
    //         return connect({ hostname: addressRemote, port: portRemote });
    //     },
    //     'default': () => {
    //         return connect({ hostname: addressRemote, port: portRemote });
    //     },
    // };
    // // const handler = connectionHandlers[mode] || connectionHandlers['default'];
    // // return handler();
    // return (connectionHandlers[mode] || connectionHandlers['default'])();

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
            // return await socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    try {
        if (!extractedID || !uuidString) {
            throw new Error('Invalid UUID parameters');
        }
        
        uuidString = uuidString.replaceAll('-', '');
        
        if (extractedID.length !== 16 || uuidString.length !== 32) {
            throw new Error('Invalid UUID length');
        }
        
        for (let index = 0; index < 16; index++) {
            const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16);
            if (isNaN(expected) || extractedID[index] !== expected) {
                return false;
            }
        }
        return true;
    } catch (error) {
        console.error('UUID matching error:', error);
        return false;
    }
}

function parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap) {
    let hostname = '';

    try {
        switch (addressType) {
            case addressTypeMap.IPv4: { // IPv4 - addressLength = 4
                if (offset + 4 > bytes.length) {
                    throw new Error('IPv4 address data insufficient');
                }
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            }
            case addressTypeMap.DOMAIN: { // Domain name - addressLength = domainLength
                if (offset >= bytes.length) {
                    throw new Error('Domain length data insufficient');
                }
                const domainLen = bytes[offset++];
                if (offset + domainLen > bytes.length) {
                    throw new Error('Domain name data insufficient');
                }
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
                break;
            }
            case addressTypeMap.IPv6: { // IPv6
                if (offset + 16 > bytes.length) {
                    throw new Error('IPv6 address data insufficient');
                }
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            case 49: { // IPv6 
                if (offset + 16 > bytes.length) {
                    throw new Error('IPv6 (type 49) address data insufficient');
                }
                for (let i = 0; i < 8; i++) {
                    hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            default: {
                throw new Error(`Unsupported address type: ${addressType}`);
            }
        }
    } catch (error) {
        console.error('Address parsing error:', error);
        throw error;
    }

    return { hostname, offset };
}

async function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    try {
        // 🛡️ 安全检查 buffer
        if (!buffer || buffer.byteLength === 0) {
            throw new Error('Invalid or empty buffer');
        }

        const bytes = new Uint8Array(buffer);
        const view = new DataView(buffer);
        const decoder = new TextDecoder();

        const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };

        if (protocolMode === globalControllerConfig.targetProtocolType0) {
            // 🛡️ 安全检查数据长度
            if (bytes.length < 17) {
                throw new Error('Protocol header too short for UUID extraction');
            }
            
            const extractedID = bytes.subarray(1, 17);
            if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    try {
                        wsInterface.close(1013, 'Invalid user');
                    } catch (closeError) {
                        console.warn('WebSocket close failed during UUID validation:', closeError);
                    }
                }
                throw new Error(`Invalid user: UUID does not match`);
            }

            if (bytes.length < 19) {
                throw new Error('Protocol header too short for command parsing');
            }

            const version = bytes[0];
            const optionsLength = bytes[17];
            const commandIndex = 18 + optionsLength;
            
            if (commandIndex >= bytes.length) {
                throw new Error('Protocol header too short for command');
            }
            
            const command = bytes[commandIndex]; // 0x01 TCP, 0x02 UDP, 0x03 MUX
            const portIndex = 18 + optionsLength + 1;
            
            if (portIndex + 2 > bytes.length) {
                throw new Error('Protocol header too short for port');
            }
            
            const port = view.getUint16(portIndex); // port is big-Endian in raw data etc 80 == 0x005d
            let offset = portIndex + 2;
            
            if (offset >= bytes.length) {
                throw new Error('Protocol header too short for address type');
            }
            
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            const rawClientData = bytes.subarray(newOffset);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData,
                version
            };
        } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
            const crLfIndex = 56;
            
            if (bytes.length < crLfIndex) {
                throw new Error('Protocol header too short for password extraction');
            }
            
            const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
            if (extractedPassword !== globalSessionConfig.user.sha224) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    try {
                        wsInterface.close(1013, 'Invalid password');
                    } catch (closeError) {
                        console.warn('WebSocket close failed during password validation:', closeError);
                    }
                }
                throw new Error(`Invalid password`);
            }

            let offset = crLfIndex + 2;
            
            if (offset + 2 > bytes.length) {
                throw new Error('Protocol header too short for command and address type');
            }
            
            const command = bytes[offset++];
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            
            if (newOffset + 2 > bytes.length) {
                throw new Error('Protocol header too short for port');
            }
            
            const port = view.getUint16(newOffset);
            const rawClientData = bytes.subarray(newOffset + 4);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData
            };
        } else {
            throw new Error(`Unsupported protocol mode: ${protocolMode}`);
        }
    } catch (error) {
        console.error('Protocol header parsing error:', error);
        throw error;
    }
}

function decodeBase64Url(encodedString) {
    try {
        // 🛡️ 安全检查输入
        if (!encodedString || typeof encodedString !== 'string') {
            throw new Error('Invalid base64 string');
        }
        
        const normalizedString = encodedString.replaceAll('-', '+').replaceAll('_', '/');
        const decoded = atob(normalizedString);
        return Uint8Array.from(decoded, (c) => c.charCodeAt(0)).buffer;
    } catch (error) {
        console.error('Base64 decode error:', error);
        throw new Error(`Base64 decode failed: ${error.message}`);
    }
}

async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    let socket = null;
    let reader = null;
    let writer = null;
    
    try {
        const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
        const encoder = new TextEncoder();
        
        // 🛡️ 安全连接到 SOCKS 服务器
        try {
            socket = await connect({ hostname, port });
        } catch (connectError) {
            console.error('SOCKS5 server connection failed:', connectError);
            throw new Error(`SOCKS5 connection failed: ${connectError.message}`);
        }
        
        // 🛡️ 安全获取 reader 和 writer
        try {
            reader = socket.readable.getReader();
            writer = socket.writable.getWriter();
        } catch (streamError) {
            console.error('SOCKS5 stream creation failed:', streamError);
            await cleanupResources();
            throw new Error(`SOCKS5 stream creation failed: ${streamError.message}`);
        }
        
        if (!reader || !writer) {
            await cleanupResources();
            throw new Error('Failed to create SOCKS5 streams');
        }
        
        // 🛡️ 安全发送 SOCKS5 握手
        try {
            const socksGreeting = new Uint8Array([5, 2, 0, 2]); // Support No Auth and Username/Password Auth
            await writer.write(socksGreeting);
        } catch (writeError) {
            console.error('SOCKS5 greeting write failed:', writeError);
            await cleanupResources();
            throw new Error(`SOCKS5 greeting failed: ${writeError.message}`);
        }
        
        // 🛡️ 安全读取服务器响应
        let res;
        try {
            const readResult = await reader.read();
            res = readResult.value;
            if (!res || res.length < 2) {
                throw new Error('Invalid SOCKS5 greeting response');
            }
        } catch (readError) {
            console.error('SOCKS5 greeting read failed:', readError);
            await cleanupResources();
            throw new Error(`SOCKS5 greeting read failed: ${readError.message}`);
        }
        
        if (res[0] !== 0x05) {
            await cleanupResources();
            throw new Error('Invalid SOCKS5 version in response');
        }
        
        if (res[1] === 0xff) {
            await cleanupResources();
            throw new Error('SOCKS5 server rejected all authentication methods');
        }
        
        // 🛡️ 安全处理认证
        if (res[1] === 0x02) {
            if (!username || !password) {
                await cleanupResources();
                throw new Error('SOCKS5 authentication required but credentials not provided');
            }
            
            try {
                const authRequest = new Uint8Array([
                    1,
                    username.length,
                    ...encoder.encode(username),
                    password.length,
                    ...encoder.encode(password)
                ]);
                await writer.write(authRequest);
                
                const authResult = await reader.read();
                res = authResult.value;
                if (!res || res.length < 2 || res[0] !== 0x01 || res[1] !== 0x00) {
                    throw new Error('SOCKS5 authentication failed');
                }
            } catch (authError) {
                console.error('SOCKS5 authentication error:', authError);
                await cleanupResources();
                throw new Error(`SOCKS5 authentication failed: ${authError.message}`);
            }
        }
        
        // 🛡️ 安全准备和发送请求
        let DSTADDR;
        const addressTypeMap = targetProtocol
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
            
        try {
            switch (addressType) {
                case addressTypeMap.IPv4:
                    const ipParts = addressRemote.split('.');
                    if (ipParts.length !== 4) {
                        throw new Error('Invalid IPv4 address format');
                    }
                    DSTADDR = new Uint8Array([1, ...ipParts.map(part => {
                        const num = Number(part);
                        if (isNaN(num) || num < 0 || num > 255) {
                            throw new Error('Invalid IPv4 address part');
                        }
                        return num;
                    })]);
                    break;
                case addressTypeMap.DOMAIN:
                    if (!addressRemote || addressRemote.length === 0 || addressRemote.length > 255) {
                        throw new Error('Invalid domain name');
                    }
                    DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                    break;
                case addressTypeMap.IPv6:
                    try {
                        DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
                    } catch (ipv6Error) {
                        throw new Error('Invalid IPv6 address format');
                    }
                    break;
                default:
                    throw new Error(`Unsupported address type: ${addressType}`);
            }
        } catch (addrError) {
            console.error('SOCKS5 address preparation failed:', addrError);
            await cleanupResources();
            throw addrError;
        }
        
        try {
            const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
            await writer.write(socksRequest);
            
            const finalResult = await reader.read();
            res = finalResult.value;
            if (!res || res.length < 2 || res[1] !== 0x00) {
                throw new Error(`SOCKS5 connection request failed: ${res ? res[1] : 'no response'}`);
            }
        } catch (requestError) {
            console.error('SOCKS5 request failed:', requestError);
            await cleanupResources();
            throw new Error(`SOCKS5 request failed: ${requestError.message}`);
        }
        
        // 🛡️ 安全释放锁
        try {
            reader.releaseLock();
            writer.releaseLock();
        } catch (releaseError) {
            console.warn('SOCKS5 lock release failed:', releaseError);
        }
        
        return socket;

    } catch (error) {
        console.error('SOCKS5 connection error:', error);
        await cleanupResources();
        throw error;
    }

    async function cleanupResources() {
        try {
            if (reader) { 
                try { reader.releaseLock(); } catch (e) { console.warn('Reader release failed:', e); }
                reader = null;
            }
            if (writer) { 
                try { writer.releaseLock(); } catch (e) { console.warn('Writer release failed:', e); }
                writer = null;
            }
            if (socket) { 
                try { await socket.close(); } catch (e) { console.warn('Socket close failed:', e); }
                socket = null;
            }
        } catch (cleanupError) {
            console.warn('SOCKS5 cleanup error:', cleanupError);
        }
    }
}

function socks5AddressParser(address) {
    try {
        if (!address || typeof address !== 'string') {
            throw new Error('Invalid SOCKS address: address is required');
        }
        
        const [latter, former] = address.split("@").reverse();
        if (!latter) {
            throw new Error('Invalid SOCKS address format: missing host:port');
        }
        
        const [hostname, port] = latter.split(":");
        if (!hostname) {
            throw new Error('Invalid SOCKS address format: missing hostname');
        }
        
        let username, password;
        if (former) {
            const formers = former.split(":");
            if (formers.length !== 2) {
                throw new Error('Invalid SOCKS address format: Expected "username:password" before "@"');
            }
            [username, password] = formers;
        }
        
        const portNum = Number(port);
        if (!port || isNaN(portNum) || portNum <= 0 || portNum > 65535) {
            throw new Error('Invalid SOCKS address format: Port must be a valid number between 1-65535');
        }
        
        return { username, password, hostname, port: portNum };
    } catch (error) {
        console.error('SOCKS address parsing error:', error);
        throw error;
    }
}

async function fetchRemoteData(url) {
    const randomVersion = Math.floor(Math.random() * (128 - 110 + 1)) + 110;

    const headers = new Headers({
        'User-Agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomVersion}.0.0.0 Safari/537.36`,
    });

    const response = await fetch(url, { headers });
    if (!response.ok) {
        throw new Error('Failed to fetch the input string');
    }
    return response.text();
}

function encryptVar(str) {
    return str.split('').map(char => {
        return (char.charCodeAt(0) << 2).toString(16);
    }).join('-_-');
}

function decryptVar(str) {
    return str.split('-_-').map(hex => {
        return String.fromCharCode(parseInt(hex, 16) >> 2);
    }).join('');
}

const protocolTypes = {
    p0: decryptVar('1d8-_-1b0-_-194-_-1cc-_-1cc'),
    p1: decryptVar('1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'),
};


function parseAddressLines(inputString) {
    const lines = inputString.trim().split('\n');
    const uniqueEntries = new Set(lines); // Use Set to remove duplicate lines
    const commentCounts = {};

    return Array.from(uniqueEntries).map(line => {
        let atValue, atType = null;
        let processedLine = line;

        // Extract type value if present
        const typeIndex = processedLine.indexOf('|');
        if (typeIndex > -1) {
            const typeValue = processedLine.substring(typeIndex + 1).trim().toLowerCase();
            atType = typeValue || null;
            processedLine = processedLine.slice(0, typeIndex).trim(); // Use the part before '|' for further processing
        }

        // Extract @ value if present
        const atIndex = processedLine.indexOf('@');
        if (atIndex > -1) {
            atValue = processedLine.substring(atIndex + 1).trim();
            processedLine = processedLine.substring(0, atIndex).trim(); // Use the part before '@' for further processing
        }

        const [addressPort, comment] = processedLine.split('#');
        let processedComment = comment ? comment.trim() : 'Unknown';

        commentCounts[processedComment] = (commentCounts[processedComment] || 0) + 1;
        processedComment += `-${commentCounts[processedComment]}`;

        if (addressPort.startsWith('[')) { // Handle IPv6 addresses
            const closingBracketIndex = addressPort.indexOf(']');
            if (closingBracketIndex > -1) {
                const address = addressPort.substring(0, closingBracketIndex + 1);
                const port = addressPort.slice(closingBracketIndex + 2).trim() || null;
                return { address, port: port || null, comment: processedComment, atValue, atType };
            }
        } else { // Handle IPv4 addresses or domain names
            const [address, port] = addressPort.trim().split(':'); // Trim to remove potential leading/trailing spaces
            return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
        }
    });
}

function generateConfigs(inputString, uuid, password, enableTls, domain) {
    const transportProtocol = 'ws';
    const fingerprint = 'chrome';

    // Get path based on protocol and additional parameters
    function getPath(protocol, atValue, atType) {
        const basePath = protocol === protocolTypes.p0
            ? '/?ed=2560'
            : '/trws?ed=2560';

        if (!atValue) return basePath;

        // Add parameters based on different types
        switch (atType?.toLowerCase()) {
            // case 'relayip':
            //     return `${basePath}&relayip=${atValue}`;
            case 'socks':
                return `${basePath}&socks=${atValue}`;
            default:
                return `${basePath}&relayip=${atValue}`;
        }
    }

    const parsedAddresses = parseAddressLines(inputString);
    const configs = {
        protocolP0: [],
        protocolP1: [],
        customP0: { fullConfig: [], namesOnly: [] },
        customP1: { fullConfig: [], namesOnly: [] }
        // customP0: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] },
        // customP1: { fullConfig: ['  - ' + JSON.stringify({ name: "Dns-out", type: "dns" })], namesOnly: [] }
    };

    // Generate protocol string with path parameters
    function generateProtocolString(protocol, credentials, address, port, comment, atValue, atType) {
        const securityType = enableTls ? 'tls' : 'none';
        const path = getPath(protocol, atValue, atType);
        const baseString = `${protocol}://${credentials}@${address}:${port}?security=${securityType}&sni=${domain}&fp=${fingerprint}&type=${transportProtocol}&host=${domain}&path=${encodeURIComponent(path)}`;
        const encryptionPart = protocol === protocolTypes.p0 ? '&encryption=none' : '';
        return `${baseString}${encryptionPart}#${encodeURIComponent(comment)}`;
    }

    // Generate custom config with path parameters
    function generateCustomConfig(protocol, address, port, comment, atValue, atType) {
        const isFirstProtocol = protocol === protocolTypes.p0;
        const nodeName = `${protocol}-${comment}`;
        const path = getPath(protocol, atValue, atType);

        return {
            name: nodeName,
            type: protocol,
            server: address,
            port: enableTls ? (port || 443) : (port || 80),
            [isFirstProtocol ? 'uuid' : 'password']: isFirstProtocol ? uuid : password,
            udp: true,
            tls: enableTls,
            network: "ws",
            [isFirstProtocol ? 'servername' : 'sni']: domain,
            ...(enableTls ? {
                "skip-cert-verify": false,
                "client-fingerprint": "chrome"
            } : {}),
            "ws-opts": {
                path: path,
                headers: {
                    Host: domain
                }
            }
        };
    }

    parsedAddresses.forEach(({ address, port, comment, atValue, atType }) => {
        const actualPort = enableTls ? (port || 443) : (port || 80);

        // Generate string configs
        const protocolP0 = generateProtocolString(protocolTypes.p0, uuid, address, actualPort, comment, atValue, atType);
        const protocolP1 = generateProtocolString(protocolTypes.p1, password, address, actualPort, comment, atValue, atType);

        configs.protocolP0.push(protocolP0.trim());
        configs.protocolP1.push(protocolP1.trim());

        // Generate custom configs
        const customP0 = generateCustomConfig(protocolTypes.p0, address, actualPort, comment, atValue, atType);
        const customP1 = generateCustomConfig(protocolTypes.p1, address, actualPort, comment, atValue, atType);

        configs.customP0.fullConfig.push('  - ' + JSON.stringify(customP0));
        configs.customP1.fullConfig.push('  - ' + JSON.stringify(customP1));
        configs.customP0.namesOnly.push(`      - "${customP0.name}"`);
        configs.customP1.namesOnly.push(`      - "${customP1.name}"`);
    });

    return {
        protocolP0: configs.protocolP0.join('\n'),
        protocolP1: configs.protocolP1.join('\n'),
        customP0: {
            fullConfig: configs.customP0.fullConfig.join('\n'),
            namesOnly: configs.customP0.namesOnly.join('\n')
        },
        customP1: {
            fullConfig: configs.customP1.fullConfig.join('\n'),
            namesOnly: configs.customP1.namesOnly.join('\n')
        }
    };
}

function getCustomConfig(inputString, uuid, pass, tls, domain, configType, inputTemplate) {
    const configs = generateConfigs(inputString, uuid, pass, tls, domain);
    let proxiesConfig, namesOnly;

    switch (configType.toLowerCase()) {
        case 'p0':
            proxiesConfig = configs.customP0.fullConfig;
            namesOnly = configs.customP0.namesOnly;
            break;
        case 'p1':
            proxiesConfig = configs.customP1.fullConfig;
            namesOnly = configs.customP1.namesOnly;
            break;
        case 'both':
            proxiesConfig = configs.customP0.fullConfig + '\n' + configs.customP1.fullConfig;
            namesOnly = configs.customP0.namesOnly + '\n' + configs.customP1.namesOnly;
            break;
        default:
            throw new Error(`Invalid configType: ${configType}. Supported types are 'p0', 'p1', and 'both'.`);
    }

    inputTemplate = inputTemplate.replace('${proxiesConfig}', proxiesConfig);
    return inputTemplate;
}