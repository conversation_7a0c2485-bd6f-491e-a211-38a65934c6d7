import { connect } from 'cloudflare:sockets';

// 🛡️ 全局异常处理器
const globalExceptionHandler = (error, context = 'Unknown') => {
    console.error(`[GLOBAL EXCEPTION] ${context}:`, error);
    // 不抛出异常，只记录
    return null;
};

// 🛡️ 安全异步包装器
const safeAsync = async (asyncFn, context = 'AsyncOperation', fallback = null) => {
    try {
        return await asyncFn();
    } catch (error) {
        globalExceptionHandler(error, context);
        return fallback;
    }
};

// 🛡️ 安全同步包装器
const safeSync = (syncFn, context = 'SyncOperation', fallback = null) => {
    try {
        return syncFn();
    } catch (error) {
        globalExceptionHandler(error, context);
        return fallback;
    }
};

// 🛡️ 安全定时器包装器
const safeTimeout = (callback, delay, context = 'Timeout') => {
    return setTimeout(() => {
        safeSync(() => callback(), context);
    }, delay);
};

const globalControllerConfig = {
    connectMode: 'direct',
    retryMode: 'relayip',
    targetProtocolType0: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d8-_-1b0-_-194-_-1cc-_-1cc',
    targetProtocolType1: '18c-_-1b0-_-1bc-_-1d4-_-190-_-198-_-1b0-_-184-_-1c8-_-194-_-e8-_-1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8',
    targetPathType0: 'vlws',
    targetPathType1: 'trws',
};

const globalSessionConfig = {
    connect: {
        connectMode: 'direct',
        retryMode: 'relayip',
    },

    user: {
        id: '49f37b98-c37f-4d46-be93-1fe0a742dd43',
        pass: 'a233255z',
        sha224: '419023a775279d21cdbda41971c0bb52e962f11b4f4bfba6015a268b',
    },

    relay: {
        ip: 'jp.almain126.changeip.biz',
        _port: null,
        get port() {
            if (this._port !== null) {
                return this._port;
            }
            return this.ip.includes(':') ? this.ip.split(':')[1] : (null || undefined);
        },
        set port(value) {
            this._port = value;
        },
        socks: 'web5.serv00.com:13668',
    },

    api: {
        addresses: 'https://rtmainalraw.pages.dev/az/index.txt',
        addresses2: 'https://rtmainalraw.pages.dev/az/main.txt',
        directTemplate: 'https://rtmainalraw.pages.dev/az/templatedirect.txt',
        globalTemplate: 'https://rtmainalraw.pages.dev/az/templateglobal.txt',
    },

    misc: {
        subName: 'myMain',
    }
};

const WS_STATES = {
    CONNECTING: 0,
    OPEN: 1,
    CLOSING: 2,
    CLOSED: 3
};

export default {
    async fetch(request, env, ctx) {
        return safeAsync(async () => {
            const { CONNECT_MODE, RETRY_MODE, USER_GUID, USER_PASS, USER_SHA224, RELAY_IP, RELAY_SOCKS, API_TXT, API_TXT_2, API_DIRECT_TEMPLATE_URL, API_GLOBAL_TEMPLATE_URL } = env;
            globalControllerConfig.connectMode = (CONNECT_MODE || globalSessionConfig.connect.connectMode).toLowerCase();
            globalControllerConfig.retryMode = (RETRY_MODE || globalSessionConfig.connect.retryMode).toLowerCase();

            globalSessionConfig.user.id = USER_GUID || globalSessionConfig.user.id;
            globalSessionConfig.user.pass = USER_PASS || globalSessionConfig.user.pass;
            globalSessionConfig.user.sha224 = USER_SHA224 || globalSessionConfig.user.sha224;
            globalSessionConfig.relay.ip = RELAY_IP || globalSessionConfig.relay.ip;
            globalSessionConfig.relay.socks = RELAY_SOCKS || globalSessionConfig.relay.socks;
            globalSessionConfig.api.addresses = API_TXT || globalSessionConfig.api.addresses;
            globalSessionConfig.api.addresses2 = API_TXT_2 || globalSessionConfig.api.addresses2;
            globalSessionConfig.api.directTemplate = API_DIRECT_TEMPLATE_URL || globalSessionConfig.api.directTemplate;
            globalSessionConfig.api.globalTemplate = API_GLOBAL_TEMPLATE_URL || globalSessionConfig.api.globalTemplate;

            const userAgent = (request.headers.get('User-Agent') || 'null').toLowerCase();
            const url = new URL(request.url);
            const upgradeHeader = request.headers.get('Upgrade');
            
            if (!upgradeHeader || upgradeHeader !== 'websocket') {
                switch (url.pathname) {
                    case '/':
                        return new Response(null, { status: 204 });
                    case `/a`:
                        return new Response(null, { status: 204 });
                    case `/z`:
                        const newResponse = new Response(null, { status: 204 });
                        console.log('Status:', newResponse.status);
                        return newResponse;
                    case `/aazz`:
                        return safeAsync(async () => {
                            const inputString = await fetchRemoteData(globalSessionConfig.api.addresses);
                            const inputTemplate = await fetchRemoteData(globalSessionConfig.api.globalTemplate);

                            const getConfig = (type, tls) =>
                                url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64')
                                    ? (() => {
                                        const configs = generateConfigs(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'));
                                        if (type === 'both') return btoa(configs.protocolP0 + '\n' + configs.protocolP1);
                                        if (type === 'p0') return btoa(configs.protocolP0);
                                        if (type === 'p1') return btoa(configs.protocolP1);
                                    })()
                                    : getCustomConfig(inputString, globalSessionConfig.user.id, globalSessionConfig.user.pass, tls, request.headers.get('Host'), type, inputTemplate);

                            const configs = {
                                both: getConfig('both', true),
                                p0: getConfig('p0', true),
                                p1: getConfig('p1', true),
                                bothNotls: getConfig('both', false),
                                p0Notls: getConfig('p0', false),
                                p1Notls: getConfig('p1', false)
                            };

                            const configMappings = [
                                { param: 'both', config: configs.both },
                                { param: 'az', config: configs.p0 },
                                { param: 'za', config: configs.p1 },
                                { param: 'both-notls', config: configs.bothNotls },
                                { param: 'az-notls', config: configs.p0Notls },
                                { param: 'za-notls', config: configs.p1Notls }
                            ];

                            const getResponseConfig = (isMozilla) => ({
                                status: 200,
                                headers: {
                                    "Content-Type": "text/plain;charset=utf-8",
                                    ...(isMozilla ? {} : {
                                        "Content-Disposition": `attachment; filename=${globalSessionConfig.misc.subName}; filename*=utf-8''${encodeURIComponent(globalSessionConfig.misc.subName)}`,
                                        "Profile-Update-Interval": "6"
                                    })
                                }
                            });

                            const isMozilla = userAgent && userAgent.includes('mozilla');
                            const prefixes = ['b64', 'base64'];

                            for (const prefix of prefixes) {
                                for (const { param, config } of configMappings) {
                                    const fullParam = `${prefix}${param}`;
                                    if (url.searchParams.has(fullParam)) {
                                        return new Response(config, getResponseConfig(isMozilla));
                                    }
                                }
                            }

                            for (const { param, config } of configMappings) {
                                if (url.searchParams.has(param)) {
                                    return new Response(config, getResponseConfig(isMozilla));
                                }
                            }

                            const isB64Request = url.searchParams.toString().includes('b64') || url.searchParams.toString().includes('base64');
                            const defaultConfig = isB64Request
                                ? getConfig('both', true)
                                : configMappings[0].config;

                            return new Response(defaultConfig, getResponseConfig(isMozilla));

                        }, 'ConfigGeneration', new Response('Config generation failed', { status: 500 }));

                    default:
                        return new Response('Not found', { status: 404 });
                }
            } else if (upgradeHeader === 'websocket') {
                if (url.searchParams.has('relayip')) {
                    globalSessionConfig.relay.ip = url.searchParams.get('relayip') || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/relayip=')) {
                    globalSessionConfig.relay.ip = url.pathname.split('/relayip=')[1]?.trim() || globalSessionConfig.relay.ip.trim();
                    globalControllerConfig.retryMode = ('relayip').toLowerCase();
                } else if (url.searchParams.has('socks')) {
                    globalSessionConfig.relay.socks = url.searchParams.get('socks') || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                } else if (url.pathname.toLowerCase().includes('/socks=')) {
                    globalSessionConfig.relay.socks = url.pathname.split('/socks=')[1]?.trim() || globalSessionConfig.relay.socks.trim();
                    globalControllerConfig.retryMode = ('relaysocks').toLowerCase();
                }

                const [relayIp, relayPort] = globalSessionConfig.relay.ip.split(':');
                globalSessionConfig.relay.ip = relayIp;
                if (relayPort) globalSessionConfig.relay.port = relayPort;

                const HANDLER_CHOICE = 1;
                const handlerConfigs = {
                    1: { sessionA: handleSession, sessionZ: handleSession },
                };
                const config = handlerConfigs[HANDLER_CHOICE];
                const handleSessionA = (request, env, ctx) => config.sessionA(request, env, ctx, globalControllerConfig.targetProtocolType0);
                const handleSessionZ = (request, env, ctx) => config.sessionZ(request, env, ctx, globalControllerConfig.targetProtocolType1);

                let handler;
                const pathType = url.pathname.split('/')[1];
                switch (pathType) {
                    case globalControllerConfig.targetPathType1:
                        handler = handleSessionZ;
                        break;
                    case globalControllerConfig.targetPathType0:
                        handler = handleSessionA;
                        break;
                    default:
                        handler = handleSessionA;
                }

                return await handler(request, env, ctx);
            }
        }, 'MainFetch', new Response('Internal Server Error', { status: 500 }));
    },
};

// 🛡️ 终极版本的 handleSession 函数
export async function handleSession(request, env, ctx, protocolMode) {
    return safeAsync(async () => {
        let client = null;
        let server = null;
        let tcpInterface = null;
        let tcpReader = null;
        let tcpWriter = null;
        let upstreamReadable = null;
        let isSessionClosed = false;
        let cleanupLock = false; // 🔒 防止重复清理

        // 🛡️ 超级安全的清理函数
        const ultraSafeCleanup = (reason = 'Unknown error') => {
            return safeSync(() => {
                if (isSessionClosed || cleanupLock) return;
                cleanupLock = true;
                isSessionClosed = true;

                console.log(`[CLEANUP] ${reason}`);

                // 清理 TCP 连接
                if (tcpReader) {
                    safeSync(() => tcpReader.releaseLock(), 'tcpReader.releaseLock');
                    tcpReader = null;
                }
                if (tcpWriter) {
                    safeSync(() => tcpWriter.releaseLock(), 'tcpWriter.releaseLock');
                    tcpWriter = null;
                }
                if (tcpInterface) {
                    safeSync(() => {
                        if (typeof tcpInterface.close === 'function') {
                            tcpInterface.close();
                        }
                    }, 'tcpInterface.close');
                    tcpInterface = null;
                }

                // 清理 WebSocket 连接
                if (server) {
                    safeSync(() => {
                        if (server.readyState === WS_STATES.OPEN || server.readyState === WS_STATES.CONNECTING) {
                            server.close(1013, reason);
                        }
                    }, 'server.close');
                }

                // 清理流
                if (upstreamReadable) {
                    safeAsync(() => upstreamReadable.cancel(reason), 'upstreamReadable.cancel');
                    upstreamReadable = null;
                }

                cleanupLock = false;
            }, 'UltraSafeCleanup');
        };

        // 🛡️ 超级安全的 WebSocket 发送函数
        const ultraSafeSend = (data) => {
            return safeSync(() => {
                if (server && server.readyState === WS_STATES.OPEN && !isSessionClosed) {
                    server.send(data);
                    return true;
                }
                return false;
            }, 'UltraSafeSend', false);
        };

        // 🛡️ 安全创建 WebSocket 对
        let webSocketPair;
        try {
            webSocketPair = safeSync(() => new WebSocketPair(), 'WebSocketPair.create');
            if (!webSocketPair) {
                throw new Error('Failed to create WebSocketPair');
            }
            const { 0: clientSocket, 1: serverSocket } = Object.values(webSocketPair);
            client = clientSocket;
            server = serverSocket;
        } catch (pairError) {
            console.error('WebSocketPair creation failed:', pairError);
            return new Response('WebSocket creation failed', { status: 500 });
        }

        // 🛡️ 安全接受连接
        const acceptResult = safeSync(() => server.accept(), 'server.accept');
        if (acceptResult === null) {
            return new Response('WebSocket accept failed', { status: 500 });
        }

        const earlyHeader = request.headers.get("sec-websocket-protocol") || "";
        let ingressMode = "transform";

        // 🛡️ 安全的事件处理器工厂
        const createSafeEventHandler = (handlerName, handlerFn) => {
            return (e) => safeSync(() => handlerFn(e), `EventHandler.${handlerName}`);
        };

        if (ingressMode === "transform") {
            upstreamReadable = safeSync(() => {
                const transformStream = new TransformStream();
                let holdWriter = null;

                const writerResult = safeSync(() => transformStream.writable.getWriter(), 'transformStream.getWriter');
                if (!writerResult) {
                    ultraSafeCleanup('Transform writer creation failed');
                    return transformStream.readable;
                }
                holdWriter = writerResult;

                // 🛡️ 安全处理 early header
                if (earlyHeader) {
                    safeAsync(async () => {
                        const earlyData = decodeBase64Url(earlyHeader);
                        if (earlyData && holdWriter) {
                            await holdWriter.write(earlyData);
                        }
                    }, 'EarlyHeader.process');
                }

                // 🛡️ 安全的事件处理器
                const handleMessage = createSafeEventHandler('message', (e) => {
                    if (holdWriter && !isSessionClosed) {
                        safeAsync(() => holdWriter.write(e.data), 'Message.write');
                    }
                });

                const handleClose = createSafeEventHandler('close', (e) => {
                    if (holdWriter && !isSessionClosed) {
                        safeAsync(() => holdWriter.close(), 'Writer.close');
                    }
                    ultraSafeCleanup('WebSocket closed');
                });

                const handleError = createSafeEventHandler('error', (e) => {
                    console.error('WebSocket error:', e);
                    if (holdWriter && !isSessionClosed) {
                        safeAsync(() => holdWriter.abort(), 'Writer.abort');
                    }
                    ultraSafeCleanup('WebSocket error');
                });

                // 🛡️ 安全绑定事件处理器
                safeSync(() => {
                    server['onmessage'] = handleMessage;
                    server['onclose'] = handleClose;
                    server['onerror'] = handleError;
                }, 'EventBinding.transform');

                return transformStream.readable;
            }, 'TransformStream.create', null);

        } else if (ingressMode === "readable") {
            upstreamReadable = safeSync(() => {
                return new ReadableStream({
                    start(controller) {
                        // 🛡️ 安全处理 early header
                        if (earlyHeader) {
                            safeSync(() => {
                                const earlyData = decodeBase64Url(earlyHeader);
                                if (earlyData) {
                                    controller.enqueue(earlyData);
                                }
                            }, 'EarlyHeader.enqueue');
                        }

                        // 🛡️ 安全的控制器操作包装器
                        const safeEnqueue = (data) => safeSync(() => {
                            if (!isSessionClosed) controller.enqueue(data);
                        }, 'Controller.enqueue');

                        const safeClose = () => safeSync(() => {
                            if (!isSessionClosed) controller.close();
                        }, 'Controller.close');

                        const safeError = (error) => safeSync(() => {
                            if (!isSessionClosed) controller.error(error);
                        }, 'Controller.error');

                        const handleMessage = createSafeEventHandler('message', (e) => {
                            safeEnqueue(e.data);
                        });

                        const handleClose = createSafeEventHandler('close', (e) => {
                            safeClose();
                            ultraSafeCleanup('WebSocket closed (readable)');
                        });

                        const handleError = createSafeEventHandler('error', (e) => {
                            console.error('WebSocket error (readable):', e);
                            safeError(e);
                            ultraSafeCleanup('WebSocket error (readable)');
                        });

                        // 🛡️ 安全绑定事件处理器
                        safeSync(() => {
                            server['onmessage'] = handleMessage;
                            server['onclose'] = handleClose;
                            server['onerror'] = handleError;
                        }, 'EventBinding.readable');
                    },
                });
            }, 'ReadableStream.create', null);
        }

        if (!upstreamReadable) {
            ultraSafeCleanup('Failed to create upstream readable');
            return new Response('Stream creation failed', { status: 500 });
        }

        // 🛡️ 终极安全的 pipeTo 操作
        const pipePromise = safeAsync(async () => {
            return upstreamReadable.pipeTo(
                new WritableStream({
                    async write(chunk) {
                        return safeAsync(async () => {
                            if (isSessionClosed) {
                                throw new Error('Session already closed');
                            }

                            if (tcpWriter) {
                                await tcpWriter.write(chunk);
                                return;
                            }

                            // 🛡️ 安全解析协议头
                            const header = await parseProtocolHeader(chunk, server, protocolMode);

                            // 🛡️ 安全的连接建立
                            try {
                                tcpInterface = await dial(header, globalControllerConfig.connectMode, protocolMode);
                                await tcpInterface.opened;
                            } catch (connectError) {
                                console.warn('Primary connection failed, trying retry mode:', connectError);
                                tcpInterface = await dial(header, globalControllerConfig.retryMode, protocolMode);
                                await tcpInterface.opened;
                            }

                            // 🛡️ 安全获取 TCP writer
                            tcpWriter = safeSync(() => tcpInterface.writable.getWriter(), 'tcpInterface.getWriter');
                            if (!tcpWriter) {
                                throw new Error('Failed to get TCP writer');
                            }

                            // 🛡️ 安全发送协议响应
                            if (protocolMode === globalControllerConfig.targetProtocolType0) {
                                const responseData = safeSync(() => Uint8Array.of(header.version, 0), 'ProtocolResponse.create');
                                if (responseData && !ultraSafeSend(responseData)) {
                                    throw new Error('Failed to send protocol response');
                                }
                            }

                            // 🛡️ 安全写入初始数据
                            if (header.rawClientData) {
                                await tcpWriter.write(header.rawClientData);
                            }

                            // 🛡️ 终极安全的 TCP 到 WebSocket 数据转发
                            safeAsync(async () => {
                                tcpReader = safeSync(() => tcpInterface.readable.getReader(), 'tcpInterface.getReader');
                                if (!tcpReader) {
                                    throw new Error('Failed to get TCP reader');
                                }

                                while (!isSessionClosed) {
                                    const result = await safeAsync(
                                        () => tcpReader.read(),
                                        'tcpReader.read',
                                        { done: true, value: null }
                                    );

                                    const { value, done } = result;
                                    if (done || !value) {
                                        console.log('TCP stream ended');
                                        break;
                                    }

                                    if (!ultraSafeSend(value)) {
                                        console.warn('WebSocket send failed, stopping TCP read loop');
                                        break;
                                    }
                                }
                            }, 'TCPToWebSocket.forward').finally(() => {
                                if (tcpReader) {
                                    safeSync(() => tcpReader.releaseLock(), 'tcpReader.releaseLock.finally');
                                }
                                ultraSafeCleanup('TCP reader finished');
                            });

                        }, 'WritableStream.write');
                    },
                    close() {
                        console.log('WritableStream closed');
                        ultraSafeCleanup('WritableStream closed');
                    },
                    abort(reason) {
                        console.error('WritableStream aborted:', reason);
                        ultraSafeCleanup('WritableStream aborted');
                    }
                })
            );
        }, 'PipeTo.operation', Promise.resolve());

        // 🛡️ 安全处理 pipeTo 结果
        if (pipePromise && typeof pipePromise.catch === 'function') {
            pipePromise.catch((pipeError) => {
                console.error('PipeTo operation failed:', pipeError);
                ultraSafeCleanup('PipeTo operation failed');
            });
        }

        // 🛡️ 安全的超时保护
        const timeoutId = safeTimeout(() => {
            if (!isSessionClosed) {
                console.warn('Session timeout, cleaning up');
                ultraSafeCleanup('Session timeout');
            }
        }, 300000, 'SessionTimeout');

        // 清理超时定时器
        if (pipePromise && typeof pipePromise.finally === 'function') {
            pipePromise.finally(() => {
                safeSync(() => clearTimeout(timeoutId), 'clearTimeout');
            });
        }

        return new Response(null, { status: 101, webSocket: client });

    }, 'HandleSession', new Response('Session Error', { status: 500 }));
}

/* Single-dial wrapper */
async function dial(header, mode, protocolMode) {
    const iface = await createConnection(header, mode, protocolMode);
    await iface.opened;
    return iface;
}

async function createConnection(header, mode, protocolMode) {
    const { addressType, addressRemote, portRemote } = header;
    const useTargetProtocol = protocolMode === globalControllerConfig.targetProtocolType0;

    switch (mode) {
        case 'relayip': {
            const needDirect =
                [1].includes(addressType) ||
                (useTargetProtocol && [3].includes(addressType)) ||
                (!useTargetProtocol && [4].includes(addressType));
            return needDirect
                ? connect({ hostname: addressRemote, port: portRemote })
                : connect({
                    hostname: globalSessionConfig.relay.ip,
                    port: globalSessionConfig.relay.port || portRemote,
                });
        }
        case 'relaysocks': {
            return socks5Connect(addressType, addressRemote, portRemote, useTargetProtocol);
        }
        case 'direct': {
            return connect({ hostname: addressRemote, port: portRemote });
        }
        default:
            return connect({ hostname: addressRemote, port: portRemote });
    }
}

function matchUuid(extractedID, uuidString) {
    return safeSync(() => {
        if (!extractedID || !uuidString) {
            throw new Error('Invalid UUID parameters');
        }
        
        uuidString = uuidString.replaceAll('-', '');
        
        if (extractedID.length !== 16 || uuidString.length !== 32) {
            throw new Error('Invalid UUID length');
        }
        
        for (let index = 0; index < 16; index++) {
            const expected = parseInt(uuidString.substring(index * 2, index * 2 + 2), 16);
            if (isNaN(expected) || extractedID[index] !== expected) {
                return false;
            }
        }
        return true;
    }, 'matchUuid', false);
}

function parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap) {
    return safeSync(() => {
        let hostname = '';

        switch (addressType) {
            case addressTypeMap.IPv4: {
                if (offset + 4 > bytes.length) {
                    throw new Error('IPv4 address data insufficient');
                }
                hostname = `${bytes[offset]}.${bytes[offset + 1]}.${bytes[offset + 2]}.${bytes[offset + 3]}`;
                offset += 4;
                break;
            }
            case addressTypeMap.DOMAIN: {
                if (offset >= bytes.length) {
                    throw new Error('Domain length data insufficient');
                }
                const domainLen = bytes[offset++];
                if (offset + domainLen > bytes.length) {
                    throw new Error('Domain name data insufficient');
                }
                hostname = decoder.decode(bytes.subarray(offset, offset + domainLen));
                offset += domainLen;
                break;
            }
            case addressTypeMap.IPv6: {
                if (offset + 16 > bytes.length) {
                    throw new Error('IPv6 address data insufficient');
                }
                hostname = view.getUint16(offset).toString(16);
                for (let i = 1; i < 8; i++) {
                    hostname += ':' + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            case 49: {
                if (offset + 16 > bytes.length) {
                    throw new Error('IPv6 (type 49) address data insufficient');
                }
                for (let i = 0; i < 8; i++) {
                    hostname += (i ? ':' : '') + view.getUint16(offset + i * 2).toString(16);
                }
                offset += 16;
                break;
            }
            default: {
                throw new Error(`Unsupported address type: ${addressType}`);
            }
        }

        return { hostname, offset };
    }, 'parseAddress', { hostname: '', offset: 0 });
}

async function parseProtocolHeader(buffer, wsInterface, protocolMode) {
    return safeAsync(async () => {
        if (!buffer || buffer.byteLength === 0) {
            throw new Error('Invalid or empty buffer');
        }

        const bytes = new Uint8Array(buffer);
        const view = new DataView(buffer);
        const decoder = new TextDecoder();

        const addressTypeMap = protocolMode === globalControllerConfig.targetProtocolType0
            ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
            : { IPv4: 1, DOMAIN: 3, IPv6: 4 };

        if (protocolMode === globalControllerConfig.targetProtocolType0) {
            if (bytes.length < 17) {
                throw new Error('Protocol header too short for UUID extraction');
            }
            
            const extractedID = bytes.subarray(1, 17);
            if (!matchUuid(extractedID, globalSessionConfig.user.id)) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    safeSync(() => wsInterface.close(1013, 'Invalid user'), 'wsInterface.close.invalidUser');
                }
                throw new Error(`Invalid user: UUID does not match`);
            }

            if (bytes.length < 19) {
                throw new Error('Protocol header too short for command parsing');
            }

            const version = bytes[0];
            const optionsLength = bytes[17];
            const commandIndex = 18 + optionsLength;
            
            if (commandIndex >= bytes.length) {
                throw new Error('Protocol header too short for command');
            }
            
            const command = bytes[commandIndex];
            const portIndex = 18 + optionsLength + 1;
            
            if (portIndex + 2 > bytes.length) {
                throw new Error('Protocol header too short for port');
            }
            
            const port = view.getUint16(portIndex);
            let offset = portIndex + 2;
            
            if (offset >= bytes.length) {
                throw new Error('Protocol header too short for address type');
            }
            
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            const rawClientData = bytes.subarray(newOffset);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData,
                version
            };
        } else if (protocolMode === globalControllerConfig.targetProtocolType1) {
            const crLfIndex = 56;
            
            if (bytes.length < crLfIndex) {
                throw new Error('Protocol header too short for password extraction');
            }
            
            const extractedPassword = decoder.decode(bytes.subarray(0, crLfIndex));
            if (extractedPassword !== globalSessionConfig.user.sha224) {
                if (wsInterface && typeof wsInterface.close === 'function') {
                    safeSync(() => wsInterface.close(1013, 'Invalid password'), 'wsInterface.close.invalidPassword');
                }
                throw new Error(`Invalid password`);
            }

            let offset = crLfIndex + 2;
            
            if (offset + 2 > bytes.length) {
                throw new Error('Protocol header too short for command and address type');
            }
            
            const command = bytes[offset++];
            const addressType = bytes[offset++];

            const { hostname, offset: newOffset } = parseAddress(bytes, view, decoder, offset, addressType, addressTypeMap);
            
            if (newOffset + 2 > bytes.length) {
                throw new Error('Protocol header too short for port');
            }
            
            const port = view.getUint16(newOffset);
            const rawClientData = bytes.subarray(newOffset + 4);

            return {
                addressType: addressType,
                addressRemote: hostname,
                portRemote: port,
                rawClientData
            };
        } else {
            throw new Error(`Unsupported protocol mode: ${protocolMode}`);
        }
    }, 'parseProtocolHeader');
}

function decodeBase64Url(encodedString) {
    return safeSync(() => {
        if (!encodedString || typeof encodedString !== 'string') {
            throw new Error('Invalid base64 string');
        }
        
        const normalizedString = encodedString.replaceAll('-', '+').replaceAll('_', '/');
        const decoded = atob(normalizedString);
        return Uint8Array.from(decoded, (c) => c.charCodeAt(0)).buffer;
    }, 'decodeBase64Url', null);
}

async function socks5Connect(addressType, addressRemote, portRemote, targetProtocol) {
    return safeAsync(async () => {
        let socket = null;
        let reader = null;
        let writer = null;
        
        const cleanupResources = () => safeSync(() => {
            if (reader) { 
                safeSync(() => reader.releaseLock(), 'socks5.reader.releaseLock');
                reader = null;
            }
            if (writer) { 
                safeSync(() => writer.releaseLock(), 'socks5.writer.releaseLock');
                writer = null;
            }
            if (socket) { 
                safeAsync(() => socket.close(), 'socks5.socket.close');
                socket = null;
            }
        }, 'socks5.cleanupResources');

        try {
            const { username, password, hostname, port } = socks5AddressParser(globalSessionConfig.relay.socks);
            const encoder = new TextEncoder();
            
            socket = await connect({ hostname, port });
            reader = socket.readable.getReader();
            writer = socket.writable.getWriter();
            
            if (!reader || !writer) {
                cleanupResources();
                throw new Error('Failed to create SOCKS5 streams');
            }
            
            const socksGreeting = new Uint8Array([5, 2, 0, 2]);
            await writer.write(socksGreeting);
            
            const readResult = await reader.read();
            const res = readResult.value;
            if (!res || res.length < 2) {
                throw new Error('Invalid SOCKS5 greeting response');
            }
            
            if (res[0] !== 0x05) {
                throw new Error('Invalid SOCKS5 version in response');
            }
            
            if (res[1] === 0xff) {
                throw new Error('SOCKS5 server rejected all authentication methods');
            }
            
            if (res[1] === 0x02) {
                if (!username || !password) {
                    throw new Error('SOCKS5 authentication required but credentials not provided');
                }
                
                const authRequest = new Uint8Array([
                    1,
                    username.length,
                    ...encoder.encode(username),
                    password.length,
                    ...encoder.encode(password)
                ]);
                await writer.write(authRequest);
                
                const authResult = await reader.read();
                const authRes = authResult.value;
                if (!authRes || authRes.length < 2 || authRes[0] !== 0x01 || authRes[1] !== 0x00) {
                    throw new Error('SOCKS5 authentication failed');
                }
            }
            
            let DSTADDR;
            const addressTypeMap = targetProtocol
                ? { IPv4: 1, DOMAIN: 2, IPv6: 3 }
                : { IPv4: 1, DOMAIN: 3, IPv6: 4 };
                
            switch (addressType) {
                case addressTypeMap.IPv4:
                    const ipParts = addressRemote.split('.');
                    if (ipParts.length !== 4) {
                        throw new Error('Invalid IPv4 address format');
                    }
                    DSTADDR = new Uint8Array([1, ...ipParts.map(part => {
                        const num = Number(part);
                        if (isNaN(num) || num < 0 || num > 255) {
                            throw new Error('Invalid IPv4 address part');
                        }
                        return num;
                    })]);
                    break;
                case addressTypeMap.DOMAIN:
                    if (!addressRemote || addressRemote.length === 0 || addressRemote.length > 255) {
                        throw new Error('Invalid domain name');
                    }
                    DSTADDR = new Uint8Array([3, addressRemote.length, ...encoder.encode(addressRemote)]);
                    break;
                case addressTypeMap.IPv6:
                    DSTADDR = new Uint8Array([4, ...addressRemote.split(':').flatMap(x => x.padStart(4, '0').match(/.{2}/g).map(y => parseInt(y, 16)))]);
                    break;
                default:
                    throw new Error(`Unsupported address type: ${addressType}`);
            }
            
            const socksRequest = new Uint8Array([5, 1, 0, ...DSTADDR, portRemote >> 8, portRemote & 0xff]);
            await writer.write(socksRequest);
            
            const finalResult = await reader.read();
            const finalRes = finalResult.value;
            if (!finalRes || finalRes.length < 2 || finalRes[1] !== 0x00) {
                throw new Error(`SOCKS5 connection request failed: ${finalRes ? finalRes[1] : 'no response'}`);
            }
            
            reader.releaseLock();
            writer.releaseLock();
            
            return socket;

        } catch (error) {
            console.error('SOCKS5 connection error:', error);
            cleanupResources();
            throw error;
        }
    }, 'socks5Connect');
}

function socks5AddressParser(address) {
    return safeSync(() => {
        if (!address || typeof address !== 'string') {
            throw new Error('Invalid SOCKS address: address is required');
        }
        
        const [latter, former] = address.split("@").reverse();
        if (!latter) {
            throw new Error('Invalid SOCKS address format: missing host:port');
        }
        
        const [hostname, port] = latter.split(":");
        if (!hostname) {
            throw new Error('Invalid SOCKS address format: missing hostname');
        }
        
        let username, password;
        if (former) {
            const formers = former.split(":");
            if (formers.length !== 2) {
                throw new Error('Invalid SOCKS address format: Expected "username:password" before "@"');
            }
            [username, password] = formers;
        }
        
        const portNum = Number(port);
        if (!port || isNaN(portNum) || portNum <= 0 || portNum > 65535) {
            throw new Error('Invalid SOCKS address format: Port must be a valid number between 1-65535');
        }
        
        return { username, password, hostname, port: portNum };
    }, 'socks5AddressParser', { username: null, password: null, hostname: 'localhost', port: 1080 });
}

async function fetchRemoteData(url) {
    const randomVersion = Math.floor(Math.random() * (128 - 110 + 1)) + 110;

    const headers = new Headers({
        'User-Agent': `Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/${randomVersion}.0.0.0 Safari/537.36`,
    });

    const response = await fetch(url, { headers });
    if (!response.ok) {
        throw new Error('Failed to fetch the input string');
    }
    return response.text();
}

function encryptVar(str) {
    return str.split('').map(char => {
        return (char.charCodeAt(0) << 2).toString(16);
    }).join('-_-');
}

function decryptVar(str) {
    return str.split('-_-').map(hex => {
        return String.fromCharCode(parseInt(hex, 16) >> 2);
    }).join('');
}

const protocolTypes = {
    p0: decryptVar('1d8-_-1b0-_-194-_-1cc-_-1cc'),
    p1: decryptVar('1d0-_-1c8-_-1bc-_-1a8-_-184-_-1b8'),
};

function parseAddressLines(inputString) {
    const lines = inputString.trim().split('\n');
    const uniqueEntries = new Set(lines);
    const commentCounts = {};

    return Array.from(uniqueEntries).map(line => {
        let atValue, atType = null;
        let processedLine = line;

        const typeIndex = processedLine.indexOf('|');
        if (typeIndex > -1) {
            const typeValue = processedLine.substring(typeIndex + 1).trim().toLowerCase();
            atType = typeValue || null;
            processedLine = processedLine.slice(0, typeIndex).trim();
        }

        const atIndex = processedLine.indexOf('@');
        if (atIndex > -1) {
            atValue = processedLine.substring(atIndex + 1).trim();
            processedLine = processedLine.substring(0, atIndex).trim();
        }

        const [addressPort, comment] = processedLine.split('#');
        let processedComment = comment ? comment.trim() : 'Unknown';

        commentCounts[processedComment] = (commentCounts[processedComment] || 0) + 1;
        processedComment += `-${commentCounts[processedComment]}`;

        if (addressPort.startsWith('[')) {
            const closingBracketIndex = addressPort.indexOf(']');
            if (closingBracketIndex > -1) {
                const address = addressPort.substring(0, closingBracketIndex + 1);
                const port = addressPort.slice(closingBracketIndex + 2).trim() || null;
                return { address, port: port || null, comment: processedComment, atValue, atType };
            }
        } else {
            const [address, port] = addressPort.trim().split(':');
            return { address: address.trim(), port: port ? port.trim() : null, comment: processedComment, atValue, atType };
        }
    });
}

function generateConfigs(inputString, uuid, password, enableTls, domain) {
    const transportProtocol = 'ws';
    const fingerprint = 'chrome';

    function getPath(protocol, atValue, atType) {
        const basePath = protocol === protocolTypes.p0
            ? '/?ed=2560'
            : '/trws?ed=2560';

        if (!atValue) return basePath;

        switch (atType?.toLowerCase()) {
            case 'socks':
                return `${basePath}&socks=${atValue}`;
            default:
                return `${basePath}&relayip=${atValue}`;
        }
    }

    const parsedAddresses = parseAddressLines(inputString);
    const configs = {
        protocolP0: [],
        protocolP1: [],
        customP0: { fullConfig: [], namesOnly: [] },
        customP1: { fullConfig: [], namesOnly: [] }
    };

    function generateProtocolString(protocol, credentials, address, port, comment, atValue, atType) {
        const securityType = enableTls ? 'tls' : 'none';
        const path = getPath(protocol, atValue, atType);
        const baseString = `${protocol}://${credentials}@${address}:${port}?security=${securityType}&sni=${domain}&fp=${fingerprint}&type=${transportProtocol}&host=${domain}&path=${encodeURIComponent(path)}`;
        const encryptionPart = protocol === protocolTypes.p0 ? '&encryption=none' : '';
        return `${baseString}${encryptionPart}#${encodeURIComponent(comment)}`;
    }

    function generateCustomConfig(protocol, address, port, comment, atValue, atType) {
        const isFirstProtocol = protocol === protocolTypes.p0;
        const nodeName = `${protocol}-${comment}`;
        const path = getPath(protocol, atValue, atType);

        return {
            name: nodeName,
            type: protocol,
            server: address,
            port: enableTls ? (port || 443) : (port || 80),
            [isFirstProtocol ? 'uuid' : 'password']: isFirstProtocol ? uuid : password,
            udp: true,
            tls: enableTls,
            network: "ws",
            [isFirstProtocol ? 'servername' : 'sni']: domain,
            ...(enableTls ? {
                "skip-cert-verify": false,
                "client-fingerprint": "chrome"
            } : {}),
            "ws-opts": {
                path: path,
                headers: {
                    Host: domain
                }
            }
        };
    }

    parsedAddresses.forEach(({ address, port, comment, atValue, atType }) => {
        const actualPort = enableTls ? (port || 443) : (port || 80);

        const protocolP0 = generateProtocolString(protocolTypes.p0, uuid, address, actualPort, comment, atValue, atType);
        const protocolP1 = generateProtocolString(protocolTypes.p1, password, address, actualPort, comment, atValue, atType);

        configs.protocolP0.push(protocolP0.trim());
        configs.protocolP1.push(protocolP1.trim());

        const customP0 = generateCustomConfig(protocolTypes.p0, address, actualPort, comment, atValue, atType);
        const customP1 = generateCustomConfig(protocolTypes.p1, address, actualPort, comment, atValue, atType);

        configs.customP0.fullConfig.push('  - ' + JSON.stringify(customP0));
        configs.customP1.fullConfig.push('  - ' + JSON.stringify(customP1));
        configs.customP0.namesOnly.push(`      - "${customP0.name}"`);
        configs.customP1.namesOnly.push(`      - "${customP1.name}"`);
    });

    return {
        protocolP0: configs.protocolP0.join('\n'),
        protocolP1: configs.protocolP1.join('\n'),
        customP0: {
            fullConfig: configs.customP0.fullConfig.join('\n'),
            namesOnly: configs.customP0.namesOnly.join('\n')
        },
        customP1: {
            fullConfig: configs.customP1.fullConfig.join('\n'),
            namesOnly: configs.customP1.namesOnly.join('\n')
        }
    };
}

function getCustomConfig(inputString, uuid, pass, tls, domain, configType, inputTemplate) {
    const configs = generateConfigs(inputString, uuid, pass, tls, domain);
    let proxiesConfig, namesOnly;

    switch (configType.toLowerCase()) {
        case 'p0':
            proxiesConfig = configs.customP0.fullConfig;
            namesOnly = configs.customP0.namesOnly;
            break;
        case 'p1':
            proxiesConfig = configs.customP1.fullConfig;
            namesOnly = configs.customP1.namesOnly;
            break;
        case 'both':
            proxiesConfig = configs.customP0.fullConfig + '\n' + configs.customP1.fullConfig;
            namesOnly = configs.customP0.namesOnly + '\n' + configs.customP1.namesOnly;
            break;
        default:
            throw new Error(`Invalid configType: ${configType}. Supported types are 'p0', 'p1', and 'both'.`);
    }

    inputTemplate = inputTemplate.replace('${proxiesConfig}', proxiesConfig);
    return inputTemplate;
}